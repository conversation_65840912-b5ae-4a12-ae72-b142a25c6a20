/* frontend/static/css/index-styles.css */

/* 回测结果卡片的特定过渡效果 (shared-styles .card has transition now) */
/* .result-card { transition: all 0.3s ease; } */

/* 页面顶部实时统计栏的特定样式 */
.live-stats-bar .alert {
    font-size: 0.875rem;
    padding: 0.6rem 1rem;
    border-radius: var(--border-radius-base);
    background-color: var(--primary-color-light);
    border-color: var(--primary-color);
    border-left-width: 1px; /* No thick left border for this one */
    color: var(--primary-color);
}
.live-stats-bar small {
    margin-right: 15px;
}
.live-stats-bar small:last-child { margin-right: 0; }
.live-stats-bar strong { font-weight: 500; }

/* --- 回测结果中日历视图相关的样式 --- */
.calendar-container {
    margin-top: 24px;
    padding: 1rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    background-color: #FDFEFF; /* Slightly off-white background for the calendar */
}
.calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}
.calendar-header h5 {
    margin-bottom: 0;
    font-weight: 600;
    color: var(--text-primary-color);
}
.calendar-header .btn {
    font-size: 0.85rem;
}

.calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 1px;
    background-color: var(--border-color); /* Grid lines color */
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-base); /* Rounded corners for the grid itself */
    overflow: hidden; /* Clip day cells to grid border-radius */
}
.calendar-day-header,
.calendar-day {
    padding: 8px 6px;
    text-align: center;
    background-color: var(--card-bg-color);
    min-height: 80px;
    font-size: 0.8rem;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
}
.calendar-day-header {
    background-color: var(--table-header-bg);
    font-weight: 500;
    min-height: auto;
    justify-content: center;
    align-items: center;
    color: var(--text-primary-color);
    padding: 0.5rem 0.25rem;
}
.calendar-day {
    justify-content: center;
    align-items: center;
    color: var(--text-secondary-color); /* Default day text color */
}
.calendar-day.has-financial-data {
    justify-content: flex-start; /* Align content to top */
    align-items: center;
}
.calendar-day.not-current-month {
    background-color: #F8F9FA; /* Slightly different for non-current month days */
    color: #AEB8C2;
}
.calendar-day.not-current-month .day-number {
    color: #AEB8C2;
}

.calendar-day .day-number {
    font-weight: 500; /* MiSans medium */
    display: block;
    line-height: 1.2;
    color: var(--text-primary-color);
    margin-bottom: 4px; /* Space between day number and financial data */
}
.calendar-day .day-number.today {
    background-color: var(--primary-color);
    color: white !important;
    border-radius: 50%;
    width: 24px; height: 24px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    font-size: 0.75rem;
    font-weight: 600;
}

.calendar-day .day-financial-details {
    width: 100%;
    text-align: center;
    margin-top: auto; /* Push to bottom if not much content */
    padding-top: 4px; /* Space above financial details */
}
.calendar-day.has-financial-data .day-financial-details {
    margin-top: 2px; /* Less margin if there's data */
}

.calendar-day .pnl-value {
    font-size: 0.8rem;
    display: block;
    line-height: 1.2;
    font-weight: 500;
}
.calendar-day .trades-count {
    font-size: 0.7rem;
    color: var(--text-secondary-color);
    display: block;
    line-height: 1.1;
    margin-top: 2px;
}
.calendar-day .day-balance {
    font-size: 0.7rem;
    color: var(--text-secondary-color); /* Balance also secondary */
    display: block;
    line-height: 1.1;
    margin-top: 2px;
}

@media (max-width: 768px) {
    .calendar-day-header, .calendar-day {
        min-height: 70px; font-size: 0.75rem; padding: 6px 4px;
    }
    .calendar-day .day-number.today { width: 22px; height: 22px; font-size: 0.7rem; }
    .calendar-day .pnl-value { font-size: 0.75rem; }
    .calendar-day .trades-count, .calendar-day .day-balance { font-size: 0.65rem; }
}
@media (max-width: 576px) {
    .calendar-day-header, .calendar-day {
        min-height: 60px; font-size: 0.7rem;
    }
    .calendar-day .day-number.today { width: 20px; height: 20px; font-size:0.65rem; }
}

/* Backtest params card specific styling */
.card-header.bg-primary.text-white { /* This comes from HTML, override with new primary */
    background-color: var(--primary-color) !important;
    color: white !important;
}
.result-card .card-header.bg-success.text-white {
    background-color: var(--success-color) !important;
    color: white !important;
}

.bg-light { /* Used for some param sections, make it consistent */
    background-color: #F8F9FA !important; /* Consistent light bg */
    border-radius: var(--border-radius-base);
}

/* 回测参数卡片中的错误提示样式优化 */
.card-body .alert-danger {
    border-radius: var(--border-radius-base);
    border-left: 4px solid #dc3545;
    background-color: #f8d7da;
    border-color: #f5c6cb;
    font-size: 0.875rem;
    line-height: 1.4;
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
.daily-return-pct {
    font-size: 0.7rem;
    font-weight: normal;
    line-height: 1.1;
    margin-top: 2px;
}
/* Trade History Card Styles */
.trade-card .card-header {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
}

.trade-card .card-body {
    padding: 1rem;
    font-size: 0.85rem;
}

.trade-detail-item {
    background-color: #f8f9fa;
    border-radius: 0.375rem;
    padding: 0.5rem 0.75rem;
    margin-bottom: 0.5rem;
    display: flex;
    flex-direction: column;
}

.trade-detail-label {
    font-size: 0.75em;
    color: #6c757d;
    margin-bottom: 0.15rem;
}

.trade-detail-value {
    font-size: 1em;
    font-weight: 600;
    line-height: 1.2;
}

.bg-success-subtle {
    background-color: rgba(var(--bs-success-rgb), 0.15) !important;
}

.bg-danger-subtle {
    background-color: rgba(var(--bs-danger-rgb), 0.15) !important;
}
/* --- 筛选器 UI 美化 --- */

/* 星期选择器 */
.weekday-selector {
    display: flex;
    justify-content: space-between;
    gap: 6px;
    margin-top: 8px;
}

.weekday-btn {
    flex-grow: 1;
    padding: 6px 4px;
    font-size: 0.8rem;
    font-weight: 500;
    text-align: center;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-base);
    background-color: #fff;
    color: var(--text-secondary-color);
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    -webkit-user-select: none; /* Safari */
    user-select: none;
}

.weekday-btn:hover {
    background-color: #f0f2f5;
    border-color: #adb5bd;
}

.weekday-btn.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    font-weight: 600;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 时间段筛选器 */
.time-filter-group {
    display: flex;
    align-items: center;
    gap: 10px;
    width: 100%;
}

.time-input-wrapper {
    flex-grow: 1;
}

.time-input-separator {
    color: var(--text-secondary-color);
    font-weight: 600;
    padding-top: 1.5rem; /* Align with input boxes */
}

#filterStartTime,
#filterEndTime {
    border: 1px solid var(--border-color);
    background-color: #fff;
    padding: 0.375rem 0.75rem;
    border-radius: var(--border-radius-base);
}

#filterStartTime:focus,
#filterEndTime:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(var(--primary-rgb), 0.25);
}

.scroller {
  height: 800px;
  overflow-y: auto;
}

/* --- 回测占位符样式 --- */
.backtest-placeholder {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border: 2px dashed var(--border-color);
    border-radius: var(--border-radius-lg);
    transition: all 0.3s ease;
}

.backtest-placeholder:hover {
    border-color: var(--primary-color);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.placeholder-content {
    max-width: 600px;
    margin: 0 auto;
}

.placeholder-icon {
    font-size: 4rem;
    color: var(--primary-color);
    opacity: 0.8;
}

.placeholder-title {
    color: var(--text-primary-color);
    font-weight: 600;
    font-size: 1.5rem;
}

.placeholder-description {
    font-size: 1.1rem;
    line-height: 1.6;
    max-width: 400px;
    margin: 0 auto;
}

.placeholder-features {
    margin: 2rem 0;
}

.feature-item {
    padding: 1.5rem 1rem;
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: var(--border-radius-base);
    border: 1px solid rgba(var(--primary-rgb), 0.1);
    transition: all 0.3s ease;
    height: 100%;
}

.feature-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-color: rgba(var(--primary-rgb), 0.2);
}

.feature-icon {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 0.75rem;
    opacity: 0.8;
}

.feature-title {
    color: var(--text-primary-color);
    font-weight: 600;
    margin-bottom: 0.5rem;
    font-size: 1rem;
}

.feature-desc {
    color: var(--text-secondary-color);
    font-size: 0.9rem;
    line-height: 1.4;
    margin-bottom: 0;
}

.placeholder-action {
    padding: 1rem;
    background-color: rgba(var(--primary-rgb), 0.05);
    border-radius: var(--border-radius-base);
    border: 1px solid rgba(var(--primary-rgb), 0.1);
    font-weight: 500;
    animation: pulse-subtle 2s infinite;
}

@keyframes pulse-subtle {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.8;
    }
}

/* 进度条式加载动画样式 */
.backtest-progress-container {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 16px;
    padding: 3rem 2rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.backtest-progress-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

.progress-header {
    text-align: center;
    margin-bottom: 2rem;
}

.progress-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
}

.progress-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: linear-gradient(135deg, #007bff, #0056b3);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    animation: rotate 2s linear infinite;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.progress-subtitle {
    color: #6c757d;
    font-size: 1rem;
    margin: 0;
}

.progress-main {
    margin-bottom: 2rem;
}

.progress-bar-container {
    background-color: #e9ecef;
    border-radius: 12px;
    height: 12px;
    overflow: hidden;
    position: relative;
    margin-bottom: 1rem;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #007bff, #28a745, #17a2b8);
    background-size: 300% 100%;
    border-radius: 12px;
    transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    animation: gradient-shift 3s ease-in-out infinite;
}

@keyframes gradient-shift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: progress-shine 2s infinite;
}

@keyframes progress-shine {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.progress-percentage {
    text-align: center;
    font-size: 1.25rem;
    font-weight: 600;
    color: #007bff;
    margin-bottom: 1rem;
}

.progress-stages {
    display: flex;
    justify-content: space-between;
    margin-bottom: 2rem;
    position: relative;
}

.progress-stages::before {
    content: '';
    position: absolute;
    top: 15px;
    left: 0;
    right: 0;
    height: 2px;
    background-color: #e9ecef;
    z-index: 1;
}

.progress-stage {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 2;
    flex: 1;
}

.stage-indicator {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: #e9ecef;
    border: 3px solid #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stage-indicator.active {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    transform: scale(1.1);
    box-shadow: 0 4px 16px rgba(0, 123, 255, 0.3);
}

.stage-indicator.completed {
    background: linear-gradient(135deg, #28a745, #1e7e34);
    color: white;
}

.stage-label {
    font-size: 0.875rem;
    color: #6c757d;
    text-align: center;
    max-width: 80px;
    line-height: 1.2;
    transition: color 0.3s ease;
}

.stage-label.active {
    color: #007bff;
    font-weight: 600;
}

.stage-label.completed {
    color: #28a745;
    font-weight: 500;
}

.progress-details {
    background-color: rgba(255, 255, 255, 0.7);
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.current-stage-info {
    text-align: center;
}

.current-stage-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.current-stage-description {
    color: #6c757d;
    font-size: 0.95rem;
    margin-bottom: 1rem;
}

.progress-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.progress-stat {
    text-align: center;
    padding: 0.75rem;
    background-color: rgba(255, 255, 255, 0.5);
    border-radius: 8px;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.progress-stat-value {
    font-size: 1.25rem;
    font-weight: 700;
    color: #007bff;
    display: block;
}

.progress-stat-label {
    font-size: 0.8rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .backtest-progress-container {
        padding: 2rem 1rem;
    }

    .progress-title {
        font-size: 1.25rem;
    }

    .progress-stages {
        flex-wrap: wrap;
        gap: 1rem;
    }

    .progress-stage {
        flex: 0 0 calc(50% - 0.5rem);
    }

    .stage-label {
        max-width: none;
    }

    .progress-stats {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .placeholder-icon {
        font-size: 3rem;
    }

    .placeholder-title {
        font-size: 1.3rem;
    }

    .placeholder-description {
        font-size: 1rem;
    }

    .feature-item {
        padding: 1rem 0.75rem;
        margin-bottom: 1rem;
    }

    .feature-icon {
        font-size: 1.5rem;
    }

    .feature-title {
        font-size: 0.9rem;
    }

    .feature-desc {
        font-size: 0.8rem;
    }
}